using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace Solnova.Web.Pages;

public class SampleModel : PageModel
{
    private readonly ILogger<SampleModel> _logger;
    private static int _pageLoadCounter = 0;

    public SampleModel(ILogger<SampleModel> logger)
    {
        _logger = logger;
    }

    [BindProperty]
    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot be longer than 100 characters")]
    public string Name { get; set; } = string.Empty;

    [BindProperty]
    [Required(ErrorMessage = "Message is required")]
    [StringLength(500, ErrorMessage = "Message cannot be longer than 500 characters")]
    public string Message { get; set; } = string.Empty;

    public DateTime CurrentTime { get; set; }
    public int PageLoadCount { get; set; }
    public string UserAgent { get; set; } = string.Empty;
    public string? SubmittedName { get; set; }
    public string? SubmittedMessage { get; set; }
    public DateTime? SubmittedAt { get; set; }

    public void OnGet()
    {
        _logger.LogInformation("Sample Razor Page loaded");
        
        CurrentTime = DateTime.Now;
        PageLoadCount = Interlocked.Increment(ref _pageLoadCounter);
        UserAgent = Request.Headers.UserAgent.ToString();
    }

    public IActionResult OnPost()
    {
        _logger.LogInformation("Sample Razor Page form submitted");
        
        CurrentTime = DateTime.Now;
        PageLoadCount = Interlocked.Increment(ref _pageLoadCounter);
        UserAgent = Request.Headers.UserAgent.ToString();

        if (!ModelState.IsValid)
        {
            return Page();
        }

        // Process the form submission
        SubmittedName = Name;
        SubmittedMessage = Message;
        SubmittedAt = DateTime.Now;

        _logger.LogInformation("Form submitted successfully by {Name} with message: {Message}", Name, Message);

        // Clear the form
        Name = string.Empty;
        Message = string.Empty;

        return Page();
    }
}
