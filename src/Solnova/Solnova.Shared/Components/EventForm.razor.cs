using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Solnova.Shared.Components;

public partial class EventForm : ComponentBase
{
    [Inject] public ILogger<EventForm> Logger { get; set; } = default!;

    [Parameter] public EventModel EventData { get; set; } = new EventModel();
    [Parameter] public EventCallback<EventModel> OnSave { get; set; }

    protected string ActiveTab { get; set; } = "details";

    protected void ToggleTimeZoneSelector()
    {
        // Implementation for time zone selector
        Logger.LogInformation("Time zone selector toggled");
    }

    protected void AddGoogleMeet()
    {
        EventData.HasGoogleMeet = true;
        Logger.LogInformation("Google Meet added to event");
    }

    protected void RemoveNotification()
    {
        // Implementation to remove notification
        Logger.LogInformation("Notification removed");
    }

    protected void AddNotification()
    {
        // Implementation to add notification
        Logger.LogInformation("Notification added");
    }

    protected async Task SaveEvent()
    {
        Logger.LogInformation("Saving event: {Title}", EventData.Title);
        await OnSave.InvokeAsync(EventData);
    }

    public class EventModel
    {
        [Required]
        public string Title { get; set; } = string.Empty;

        public DateTime StartDate { get; set; } = DateTime.Today;
        public DateTime EndDate { get; set; } = DateTime.Today;
        public TimeOnly StartTime { get; set; } = TimeOnly.ParseExact("11:30", "HH:mm");
        public TimeOnly EndTime { get; set; } = TimeOnly.ParseExact("12:30", "HH:mm");

        public bool IsAllDay { get; set; }
        public string RepeatOption { get; set; } = "none";

        public string Location { get; set; } = string.Empty;
        public bool HasGoogleMeet { get; set; }

        public string NotificationType { get; set; } = "notification";
        public int NotificationTime { get; set; } = 30;
        public string NotificationUnit { get; set; } = "minutes";

        public string CalendarOwner { get; set; } = "Default Calendar";
        public string BusyStatus { get; set; } = "busy";
        public string Visibility { get; set; } = "default";

        public string GuestInput { get; set; } = string.Empty;
        public bool CanModify { get; set; }
        public bool CanInvite { get; set; } = true;
        public bool CanSeeGuests { get; set; } = true;
    }
}
