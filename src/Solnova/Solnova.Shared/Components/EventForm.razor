<div class="event-form">
    <div class="form-header">
        <input type="text" class="title-input" placeholder="Add title" @bind="EventData.Title" />
    </div>

    <div class="date-time-section">
        <div class="date-picker">
            <input type="date" @bind="@(EventData.StartDate)" @bind:format="yyyy-MM-dd" />
        </div>
        <div class="time-picker">
            <input type="time" @bind="EventData.StartTime" @bind:format="HH:mm" />
        </div>
        <span class="to-separator">to</span>
        <div class="time-picker">
            <input type="time" @bind="EventData.EndTime" @bind:format="HH:mm" />
        </div>
        <div class="date-picker">
            <input type="date" @bind="EventData.EndDate" @bind:format="yyyy-MM-dd" />
        </div>
        <button class="time-zone-btn" @onclick="ToggleTimeZoneSelector">Time zone</button>
    </div>

    <div class="all-day-section">
        <label>
            <input type="checkbox" @bind="EventData.IsAllDay" />
            All day
        </label>
        <div class="repeat-selector">
            <select @bind="EventData.RepeatOption">
                <option value="none">Does not repeat</option>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
            </select>
        </div>
    </div>

    <div class="tabs">
        <button class="tab-btn @(ActiveTab == "details" ? "active" : "")" @onclick="@((e => ActiveTab = "details"))">Event details</button>
        <button class="tab-btn @(ActiveTab == "time" ? "active" : "")" @onclick="@(() => ActiveTab = "time")">Find a time</button>
        <button class="tab-btn @(ActiveTab == "guests" ? "active" : "")" @onclick="@(() => ActiveTab = "guests")">Guests</button>
    </div>

    @if (ActiveTab == "details")
    {
        <div class="details-tab">
            <div class="meet-option">
                <img src="_content/Solnova.Shared/images/meet-icon.svg" alt="Meet" />
                <button class="meet-btn" @onclick="AddGoogleMeet">Add Google Meet video conferencing</button>
            </div>

            <div class="location-input">
                <input type="text" placeholder="Add location" @bind="EventData.Location" />
            </div>

            <div class="notification-section">
                <div class="notification-dropdown">
                    <select @bind="EventData.NotificationType">
                        <option value="notification">Notification</option>
                        <option value="email">Email</option>
                    </select>
                </div>
                <div class="notification-time">
                    <input type="number" @bind="EventData.NotificationTime" />
                </div>
                <div class="notification-unit">
                    <select @bind="EventData.NotificationUnit">
                        <option value="minutes">minutes</option>
                        <option value="hours">hours</option>
                        <option value="days">days</option>
                    </select>
                </div>
                <button class="remove-btn" @onclick="RemoveNotification">×</button>
            </div>

            <button class="add-notification-btn" @onclick="AddNotification">Add notification</button>

            <div class="calendar-section">
                <div class="calendar-label">@EventData.CalendarOwner</div>
                <div class="calendar-status">
                    <select @bind="EventData.BusyStatus">
                        <option value="busy">Busy</option>
                        <option value="free">Free</option>
                    </select>
                </div>
                <div class="visibility-dropdown">
                    <select @bind="EventData.Visibility">
                        <option value="default">Default visibility</option>
                        <option value="public">Public</option>
                        <option value="private">Private</option>
                    </select>
                </div>
            </div>
        </div>
    }
    else if (ActiveTab == "guests")
    {
        <div class="guests-tab">
            <div class="guests-input">
                <input type="text" placeholder="Add guests" @bind="EventData.GuestInput" />
            </div>

            <div class="guest-permissions">
                <h3>Guest permissions</h3>
                <div class="permission-option">
                    <label>
                        <input type="checkbox" @bind="EventData.CanModify" />
                        Modify event
                    </label>
                </div>
                <div class="permission-option">
                    <label>
                        <input type="checkbox" @bind="EventData.CanInvite" />
                        Invite others
                    </label>
                </div>
                <div class="permission-option">
                    <label>
                        <input type="checkbox" @bind="EventData.CanSeeGuests" />
                        See guest list
                    </label>
                </div>
            </div>
        </div>
    }

    <div class="form-actions">
        <button class="save-btn" @onclick="SaveEvent">Save</button>
    </div>
</div>
